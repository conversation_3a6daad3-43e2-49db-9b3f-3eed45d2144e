import { OrderEntity } from "@/core.constants";
import { firestore } from "@/root-context";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  DocumentSnapshot,
  getDocs,
  getCountFromServer,
  limit,
  orderBy,
  query,
  startAfter,
  updateDoc,
  where,
  writeBatch,
} from "firebase/firestore";

const COLLECTION_NAME = "orders";

// Interfaces for order filtering and pagination
export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: any; // DocumentSnapshot for pagination
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: any; // DocumentSnapshot for next page
  hasMore: boolean;
}

/**
 * Caching system for order queries
 *
 * Features:
 * - Caches order queries for 2 minutes to reduce Firebase reads
 * - Includes filters and pagination in cache keys (including lastDoc ID)
 * - Automatically invalidates cache on create/update/delete operations
 * - Separate cache for buyers and sellers queries
 * - Supports pagination caching: each page with specific filters is cached separately
 */
interface CachedOrderResult {
  data: PaginatedOrdersResult;
  timestamp: number;
}

const ordersCache = new Map<string, CachedOrderResult>();
const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes in milliseconds

// Cache management functions
const isCacheValid = (timestamp: number): boolean => {
  return Date.now() - timestamp < CACHE_DURATION;
};

const clearCache = () => {
  ordersCache.clear();
};

const generateCacheKey = (functionName: string, filters: OrderFilters): string => {
  // Create a deterministic cache key based on function name and filters including pagination
  const filterKey = JSON.stringify({
    minPrice: filters.minPrice,
    maxPrice: filters.maxPrice,
    collectionId: filters.collectionId,
    sortBy: filters.sortBy,
    limit: filters.limit,
    // Include lastDoc ID for pagination-aware caching
    lastDocId: filters.lastDoc?.id || null,
  });
  return `${functionName}:${filterKey}`;
};

const getCachedResult = (functionName: string, filters: OrderFilters): PaginatedOrdersResult | null => {
  const cacheKey = generateCacheKey(functionName, filters);
  const cached = ordersCache.get(cacheKey);

  if (cached && isCacheValid(cached.timestamp)) {
    return cached.data;
  }

  // Remove expired cache entry
  if (cached) {
    ordersCache.delete(cacheKey);
  }

  return null;
};

const setCachedResult = (functionName: string, filters: OrderFilters, data: PaginatedOrdersResult): void => {
  const cacheKey = generateCacheKey(functionName, filters);
  ordersCache.set(cacheKey, {
    data,
    timestamp: Date.now()
  });
};

export const createOrder = async (
  orderData: Omit<OrderEntity, "id" | "createdAt" | "updatedAt">
) => {
  try {
    // Filter out undefined values to avoid Firestore errors
    const cleanOrderData = Object.fromEntries(
      Object.entries(orderData).filter(([, value]) => value !== undefined)
    );

    const docRef = await addDoc(collection(firestore, COLLECTION_NAME), {
      ...cleanOrderData,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Clear cache after creating order
    clearCache();

    return docRef.id;
  } catch (error) {
    console.error("Error creating order:", error);
    throw error;
  }
};

export const updateOrder = async (
  id: string,
  orderData: Partial<OrderEntity>
) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...orderData,
      updatedAt: new Date(),
    });

    // Clear cache after updating order
    clearCache();
  } catch (error) {
    console.error("Error updating order:", error);
    throw error;
  }
};

export const deleteOrder = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);

    // Clear cache after deleting order
    clearCache();
  } catch (error) {
    console.error("Error deleting order:", error);
    throw error;
  }
};

export const getOrders = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot
) => {
  try {
    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy("createdAt", "desc"),
      limit(pageSize)
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy("createdAt", "desc"),
        startAfter(lastDoc),
        limit(pageSize)
      );
    }

    const snapshot = await getDocs(q);
    const orders: (OrderEntity & { id: string })[] = [];

    snapshot.forEach((doc) => {
      orders.push({ id: doc.id, ...doc.data() } as OrderEntity & {
        id: string;
      });
    });

    return {
      orders,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error("Error fetching orders:", error);
    throw error;
  }
};

export const clearAllOrders = async () => {
  try {
    const snapshot = await getDocs(collection(firestore, COLLECTION_NAME));
    const batch = writeBatch(firestore);

    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Deleted ${snapshot.docs.length} orders`);

    // Clear cache after bulk delete
    clearCache();
  } catch (error) {
    console.error("Error clearing orders:", error);
    throw error;
  }
};

export const createBulkOrders = async (
  orders: Omit<OrderEntity, "id" | "createdAt" | "updatedAt">[]
) => {
  try {
    const batch = writeBatch(firestore);
    const orderCollection = collection(firestore, COLLECTION_NAME);

    orders.forEach((orderData) => {
      const docRef = doc(orderCollection);

      // Filter out undefined values to avoid Firestore errors
      const cleanOrderData = Object.fromEntries(
        Object.entries(orderData).filter(([, value]) => value !== undefined)
      );

      batch.set(docRef, {
        ...cleanOrderData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    });

    await batch.commit();
    console.log(`Created ${orders.length} orders`);

    // Clear cache after bulk create
    clearCache();
  } catch (error) {
    console.error("Error creating bulk orders:", error);
    throw error;
  }
};

export const getPaidOrdersCount = async (): Promise<number> => {
  try {
    const q = query(
      collection(firestore, COLLECTION_NAME),
      where("status", "==", "paid")
    );

    const snapshot = await getCountFromServer(q);
    return snapshot.data().count;
  } catch (error) {
    console.error("Error getting paid orders count:", error);
    throw error;
  }
};



export const getOrdersForSellers = async (filters: OrderFilters = {}): Promise<PaginatedOrdersResult> => {
  try {
    // Check cache first (including pagination)
    const cachedResult = getCachedResult('getOrdersForSellers', filters);
    if (cachedResult) {
      console.log('🎯 Returning cached result for getOrdersForSellers');
      return cachedResult;
    }

    const pageSize = filters.limit || 20;
    console.log('🔍 getOrdersForSellers called with filters:', filters);

    // For sellers tab: get orders where buyerId exists and sellerId is null/doesn't exist (buyers looking for sellers)
    let q = query(
      collection(firestore, COLLECTION_NAME),
      where("status", "==", "active"),
      where("buyerId", "!=", null)
    );

    if (filters.collectionId) {
      q = query(q, where("collectionId", "==", filters.collectionId));
    }

    // Apply sorting
    if (filters.sortBy) {
      if (filters.sortBy === 'price_asc') {
        q = query(q, orderBy("amount", "asc"));
      } else if (filters.sortBy === 'price_desc') {
        q = query(q, orderBy("amount", "desc"));
      } else if (filters.sortBy === 'date_asc') {
        q = query(q, orderBy("createdAt", "asc"));
      } else {
        q = query(q, orderBy("createdAt", "desc"));
      }
    } else {
      q = query(q, orderBy("createdAt", "desc"));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }
    q = query(q, limit(pageSize + 1)); // Get one extra to check if there are more

    const snapshot = await getDocs(q);
    console.log('📊 Raw query returned', snapshot.docs.length, 'documents');
    let orders: OrderEntity[] = [];
    let lastDoc = null;
    let hasMore = false;

    const docs = snapshot.docs;
    let processedCount = 0;

    for (let index = 0; index < docs.length; index++) {
      const doc = docs[index];

      if (processedCount >= pageSize) {
        hasMore = true; // There's at least one more document
        break;
      }

      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;
      console.log('📋 Order data:', {
        id: orderData.id,
        buyerId: orderData.buyerId,
        sellerId: orderData.sellerId,
        status: orderData.status
      });

      // For sellers tab: only include orders where sellerId is null or doesn't exist
      if (!orderData.sellerId) {
        orders.push(orderData);
        lastDoc = doc; // Only set lastDoc when we actually include an order
        processedCount++;
        console.log('✅ Order included for sellers tab');
      } else {
        console.log('❌ Order excluded (has sellerId)');
      }
    }

    // Apply price filters client-side
    if (filters.minPrice !== undefined) {
      orders = orders.filter(order => order.amount >= filters.minPrice!);
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter(order => order.amount <= filters.maxPrice!);
    }

    console.log('🎯 Final result for sellers:', {
      ordersCount: orders.length,
      hasMore,
      lastDoc: !!lastDoc
    });

    const result = {
      orders,
      lastDoc,
      hasMore
    };

    // Cache the result (including pagination)
    setCachedResult('getOrdersForSellers', filters, result);

    return result;
  } catch (error) {
    console.error("Error fetching orders for sellers:", error);
    throw error;
  }
};

export const getOrdersForBuyers = async (filters: OrderFilters = {}): Promise<PaginatedOrdersResult> => {
  try {
    // Check cache first (including pagination)
    const cachedResult = getCachedResult('getOrdersForBuyers', filters);
    if (cachedResult) {
      console.log('🎯 Returning cached result for getOrdersForBuyers');
      return cachedResult;
    }

    const pageSize = filters.limit || 20;
    console.log('🔍 getOrdersForBuyers called with filters:', filters);

    // For buyers tab: get orders where sellerId exists and buyerId is null/doesn't exist (sellers looking for buyers)
    let q = query(
      collection(firestore, COLLECTION_NAME),
      where("status", "==", "active"),
      where("sellerId", "!=", null)
    );

    if (filters.collectionId) {
      q = query(q, where("collectionId", "==", filters.collectionId));
    }

    // Apply sorting
    if (filters.sortBy) {
      if (filters.sortBy === 'price_asc') {
        q = query(q, orderBy("amount", "asc"));
      } else if (filters.sortBy === 'price_desc') {
        q = query(q, orderBy("amount", "desc"));
      } else if (filters.sortBy === 'date_asc') {
        q = query(q, orderBy("createdAt", "asc"));
      } else {
        q = query(q, orderBy("createdAt", "desc"));
      }
    } else {
      q = query(q, orderBy("createdAt", "desc"));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }
    q = query(q, limit(pageSize + 1)); // Get one extra to check if there are more

    const snapshot = await getDocs(q);
    let orders: OrderEntity[] = [];
    let lastDoc = null;
    let hasMore = false;

    const docs = snapshot.docs;
    let processedCount = 0;

    for (let index = 0; index < docs.length; index++) {
      const doc = docs[index];

      if (processedCount >= pageSize) {
        hasMore = true; // There's at least one more document
        break;
      }

      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;

      // For buyers tab: only include orders where buyerId is null or doesn't exist
      if (!orderData.buyerId) {
        orders.push(orderData);
        lastDoc = doc; // Only set lastDoc when we actually include an order
        processedCount++;
      }
    }

    // Apply price filters client-side
    if (filters.minPrice !== undefined) {
      orders = orders.filter(order => order.amount >= filters.minPrice!);
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter(order => order.amount <= filters.maxPrice!);
    }

    const result = {
      orders,
      lastDoc,
      hasMore
    };

    // Cache the result (including pagination)
    setCachedResult('getOrdersForBuyers', filters, result);

    return result;
  } catch (error) {
    console.error("Error fetching orders for buyers:", error);
    throw error;
  }
};

// Debug function to see all orders in the database
export const debugAllOrders = async (): Promise<void> => {
  try {
    console.log('🔍 DEBUG: Fetching all orders from database...');
    const q = query(collection(firestore, COLLECTION_NAME));
    const snapshot = await getDocs(q);

    console.log(`📊 Total orders in database: ${snapshot.docs.length}`);

    snapshot.docs.forEach((doc, index) => {
      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;
      console.log(`📋 Order ${index + 1}:`, {
        id: orderData.id,
        buyerId: orderData.buyerId,
        sellerId: orderData.sellerId,
        status: orderData.status,
        amount: orderData.amount,
        collectionId: orderData.collectionId,
        createdAt: orderData.createdAt
      });
    });
  } catch (error) {
    console.error("Error fetching all orders:", error);
  }
};

/**
 * Public function to manually clear the orders cache
 */
export const clearOrdersCache = () => {
  clearCache();
};
