"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Bad<PERSON>, Caption, Chip, Title } from "@telegram-apps/telegram-ui";
import { OrderEntity, Collection } from "@/core.constants";
import Image from "next/image";
import TgsViewer from "@/components/TgsViewer";
import { Button } from "@telegram-apps/telegram-ui";
import { TonLogo } from "@/components/TonLogo";
import { CardChip } from "@telegram-apps/telegram-ui/dist/components/Blocks/Card/components/CardChip/CardChip";

interface OrderCardProps {
  order: OrderEntity;
  collection: Collection | undefined;
  onClick: () => void;
  animated?: boolean;
}

export function OrderCard({
  animated,
  order,
  collection,
  onClick,
}: OrderCardProps) {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-2 flex flex-col">
        {/* Order Image */}
        <div className="aspect-square relative rounded-lg overflow-hidden bg-[#17212b]">
          {animated ? (
            <TgsViewer
              tgsUrl={`/limited/${order.collectionId}/Original.tgs`}
              style={{ height: "auto", width: "auto", padding: "16px" }}
            />
          ) : (
            <Image
              src={`/limited/${order.collectionId}/Original.png`}
              alt={collection?.name || "Order item"}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
              onError={handleImageError}
            />
          )}
        </div>

        {/* Order Number and Status */}
        <div className="flex items-center justify-between mt-2 mb-2">
          <Caption level="1" weight="1" className="truncate">
            {collection?.name || "Unknown Collection"}
          </Caption>
          <Caption level="2" weight="3" className="w-fit text-[#78797e]">
            #
            {order.number ||
              (typeof order.id === "string" ? order.id?.slice(-6) : "N/A")}
          </Caption>
        </div>

        <Button className="w-full [&>h6]:flex [&>h6]:items-center [&>h6]:justify-center [&>h6]:gap-1">
          {order.amount} <TonLogo size={16} className="w-7 h-7 -ml-2" />
        </Button>
      </CardContent>
    </Card>
  );
}
