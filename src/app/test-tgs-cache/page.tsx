"use client";

import { useState } from "react";
import TgsViewer from "@/components/TgsViewer";
import { Button } from "@/components/ui/button";
import { getTgsCacheStats, clearTgsCache } from "@/utils/tgs-cache";

export default function TestTgsCachePage() {
  const [showMultiple, setShowMultiple] = useState(false);
  const [cacheStats, setCacheStats] = useState<any>(null);

  const updateCacheStats = () => {
    setCacheStats(getTgsCacheStats());
  };

  const handleClearCache = () => {
    clearTgsCache();
    updateCacheStats();
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">TGS Cache Test</h1>
        
        <div className="mb-6 p-4 bg-gray-100 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Cache Statistics</h2>
          <div className="flex gap-4 mb-4">
            <Button onClick={updateCacheStats}>Update Stats</Button>
            <Button onClick={handleClearCache} variant="destructive">Clear Cache</Button>
          </div>
          {cacheStats && (
            <div className="text-sm">
              <p>Total Entries: {cacheStats.totalEntries}</p>
              <p>Valid Entries: {cacheStats.validEntries}</p>
              <p>Expired Entries: {cacheStats.expiredEntries}</p>
              <p>Cache Duration: {cacheStats.cacheDurationMinutes} minutes</p>
            </div>
          )}
        </div>

        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">Single TGS Viewer</h2>
          <p className="text-sm text-gray-600 mb-4">
            This loads the Backyard.tgs file once. Check the console for cache messages.
          </p>
          <div className="w-48 h-48 border rounded-lg">
            <TgsViewer
              tgsUrl="/marketplace/Backyard.tgs"
              style={{ width: "100%", height: "100%" }}
            />
          </div>
        </div>

        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">Multiple TGS Viewers</h2>
          <p className="text-sm text-gray-600 mb-4">
            Click to show multiple instances of the same TGS file. The second and subsequent instances should load from cache.
          </p>
          <Button onClick={() => setShowMultiple(!showMultiple)} className="mb-4">
            {showMultiple ? "Hide" : "Show"} Multiple Viewers
          </Button>
          
          {showMultiple && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {Array.from({ length: 6 }, (_, i) => (
                <div key={i} className="w-32 h-32 border rounded-lg">
                  <TgsViewer
                    tgsUrl="/marketplace/Backyard.tgs"
                    style={{ width: "100%", height: "100%" }}
                  />
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">Different TGS Files</h2>
          <p className="text-sm text-gray-600 mb-4">
            These load different TGS files to test cache with multiple URLs.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="w-48 h-48 border rounded-lg">
              <TgsViewer
                tgsUrl="/limited/1/Original.tgs"
                style={{ width: "100%", height: "100%" }}
              />
            </div>
            <div className="w-48 h-48 border rounded-lg">
              <TgsViewer
                tgsUrl="/limited/2/Original.tgs"
                style={{ width: "100%", height: "100%" }}
              />
            </div>
          </div>
        </div>

        <div className="text-sm text-gray-600">
          <h3 className="font-semibold mb-2">How to test:</h3>
          <ol className="list-decimal list-inside space-y-1">
            <li>Open browser console to see cache messages</li>
            <li>Load the page - first load should fetch from network</li>
            <li>Click "Show Multiple Viewers" - subsequent loads should use cache</li>
            <li>Use "Update Stats" to see cache statistics</li>
            <li>Use "Clear Cache" to reset and test again</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
