"use client";

import * as React from "react";
import { Check, ChevronDown, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Collection } from "@/core.constants";
import Image from "next/image";
import TgsViewer from "../TgsViewer";

interface CollectionSelectProps {
  animated?: boolean;
  collections: Collection[];
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export function CollectionSelect({
  animated,
  collections,
  value,
  onValueChange,
  placeholder = "Select collection...",
  className,
}: CollectionSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  const filteredCollections = React.useMemo(() => {
    if (!searchQuery) return collections;
    return collections.filter((collection) =>
      collection.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [collections, searchQuery]);

  const selectedCollection = collections.find(
    (collection) => collection.id === value
  );

  const handleSelect = (collectionId: string) => {
    onValueChange(collectionId);
    setOpen(false);
    setSearchQuery("");
  };

  return (
    <>
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={open}
        onClick={() => setOpen(true)}
        className={cn(
          "w-full justify-between h-9 px-3 py-2 text-sm bg-transparent border-gray-600 text-white hover:bg-slate-700",
          !value && "text-gray-400",
          className
        )}
      >
        {selectedCollection ? (
          <div className="flex items-center gap-2 min-w-0">
            <div className="relative w-6 h-6 rounded-sm overflow-hidden bg-slate-700 flex-shrink-0">
              <Image
                src={`/limited/${selectedCollection.id}/Original.png`}
                alt={selectedCollection.name}
                fill
                className="object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = "none";
                }}
              />
            </div>
            <span className="truncate text-white">
              {selectedCollection.name}
            </span>
          </div>
        ) : (
          <span className="text-gray-400">{placeholder}</span>
        )}
        <ChevronDown
          className={cn(
            "ml-2 h-4 w-4 shrink-0 opacity-50 text-gray-400 transition-transform",
            open && "rotate-180"
          )}
        />
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="collection-select-dialog bg-slate-800 border-slate-600 text-white p-6 translate-0">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-white text-lg font-semibold">
              Select Collection
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-col space-y-4 min-h-0">
            {/* Search Input */}
            <div className="flex items-center border border-slate-600 rounded-lg px-3 py-2 bg-slate-700/50 flex-shrink-0">
              <Search className="mr-2 h-4 w-4 shrink-0 opacity-50 text-gray-400" />
              <Input
                placeholder="Search collections..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 h-8 bg-transparent text-white placeholder:text-gray-400"
              />
            </div>

            {/* Collections Grid */}
            <div className="overflow-y-auto max-h-96">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                {/* All Collections option */}
                <div
                  className={cn(
                    "relative flex flex-col items-center p-3 rounded-lg cursor-pointer transition-all duration-200 border-2 min-h-[120px]",
                    value === "all"
                      ? "bg-ton-main/20 border-ton-main"
                      : "bg-slate-700/50 border-slate-600 hover:bg-slate-700/80 hover:border-slate-500"
                  )}
                  onClick={() => handleSelect("all")}
                >
                  <div className="w-full h-full rounded-lg bg-gradient-to-br from-slate-600 to-slate-700 flex items-center justify-center mb-2 shadow-lg">
                    <span className="text-lg text-white font-bold">All</span>
                  </div>
                  <span className="text-xs text-center text-white font-medium leading-tight">
                    All Collections
                  </span>
                  {value === "all" && (
                    <div className="absolute top-4 right-4">
                      <Check className="h-4 w-4 text-ton-main" />
                    </div>
                  )}
                </div>

                {/* Collection tiles */}
                {filteredCollections.length === 0 && searchQuery ? (
                  <div className="col-span-full py-8 text-center text-sm text-gray-400">
                    No collections found matching "{searchQuery}".
                  </div>
                ) : (
                  filteredCollections.map((collection) => (
                    <div
                      key={collection.id}
                      className={cn(
                        "relative flex flex-col items-center p-3 rounded-lg cursor-pointer transition-all duration-200 border-2 min-h-[120px]",
                        value === collection.id
                          ? "bg-ton-main/20 border-ton-main"
                          : "bg-slate-700/50 border-slate-600 hover:bg-slate-700/80 hover:border-slate-500"
                      )}
                      onClick={() => handleSelect(collection.id)}
                    >
                      <div className="w-full aspect-square relative rounded-lg overflow-hidden bg-slate-700 mb-3">
                        {animated ? (
                          <TgsViewer
                            tgsUrl={`/limited/${collection.id}/Original.tgs`}
                            style={{
                              height: "auto",
                              width: "auto",
                              padding: "16px",
                            }}
                          />
                        ) : (
                          <Image
                            src={`/limited/${collection.id}/Original.png`}
                            alt={collection.name}
                            fill
                            className="object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = "none";
                            }}
                          />
                        )}
                      </div>
                      <span className="text-xs text-center text-white font-medium line-clamp-2 leading-tight truncate w-full">
                        {collection.name}
                      </span>
                      {value === collection.id && (
                        <div className="absolute top-4 right-4">
                          <Check className="h-4 w-4 text-ton-main" />
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
